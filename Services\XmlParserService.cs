using System.Text;
using System.Xml;
using System.Xml.Serialization;
using GameItemEditor.Models;

namespace GameItemEditor.Services
{
    /// <summary>
    /// XML解析服务类
    /// 负责读取和解析游戏数据XML文件
    /// </summary>
    public class XmlParserService
    {
        private readonly string _dataDirectory;
        private readonly Dictionary<string, string> _stringMappings;

        public XmlParserService(string dataDirectory)
        {
            _dataDirectory = dataDirectory;
            _stringMappings = new Dictionary<string, string>();
            
            // 注册编码提供程序以支持中文编码
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
        }

        /// <summary>
        /// 加载所有字符串映射
        /// </summary>
        public async Task LoadStringMappingsAsync()
        {
            _stringMappings.Clear();
            
            var stringsDirectory = Path.Combine(_dataDirectory, "strings");
            if (!Directory.Exists(stringsDirectory))
                return;

            var stringFiles = Directory.GetFiles(stringsDirectory, "*.xml");
            
            foreach (var file in stringFiles)
            {
                try
                {
                    await LoadStringFileAsync(file);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"加载字符串文件失败: {file}, 错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 加载单个字符串文件
        /// </summary>
        private async Task LoadStringFileAsync(string filePath)
        {
            try
            {
                // 尝试不同的编码方式
                var encodings = new[] { Encoding.UTF8, Encoding.GetEncoding("GB2312"), Encoding.GetEncoding("UTF-16") };
                
                foreach (var encoding in encodings)
                {
                    try
                    {
                        var content = await File.ReadAllTextAsync(filePath, encoding);
                        
                        // 检查是否包含中文字符，如果包含则认为编码正确
                        if (ContainsChinese(content))
                        {
                            ParseStringContent(content, filePath);
                            return;
                        }
                    }
                    catch
                    {
                        continue;
                    }
                }
                
                // 如果所有编码都失败，使用默认UTF8
                var defaultContent = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
                ParseStringContent(defaultContent, filePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析字符串文件失败: {filePath}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析字符串文件内容
        /// </summary>
        private void ParseStringContent(string content, string filePath)
        {
            try
            {
                var doc = new XmlDocument();
                doc.LoadXml(content);
                
                var stringNodes = doc.SelectNodes("//string");
                if (stringNodes != null)
                {
                    foreach (XmlNode node in stringNodes)
                    {
                        var nameAttr = node.Attributes?["name"];
                        var bodyAttr = node.Attributes?["body"];
                        
                        if (nameAttr != null && bodyAttr != null)
                        {
                            var key = nameAttr.Value;
                            var value = bodyAttr.Value;
                            
                            if (!string.IsNullOrEmpty(key) && !string.IsNullOrEmpty(value))
                            {
                                _stringMappings[key] = value;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析XML内容失败: {filePath}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查字符串是否包含中文字符
        /// </summary>
        private bool ContainsChinese(string text)
        {
            return text.Any(c => c >= 0x4e00 && c <= 0x9fbb);
        }

        /// <summary>
        /// 获取字符串映射
        /// </summary>
        public string GetString(string key)
        {
            return _stringMappings.TryGetValue(key, out var value) ? value : key;
        }

        /// <summary>
        /// 搜索包含指定中文文本的字符串键
        /// </summary>
        public List<string> SearchStringKeys(string chineseText)
        {
            var results = new List<string>();
            
            foreach (var kvp in _stringMappings)
            {
                if (kvp.Value.Contains(chineseText, StringComparison.OrdinalIgnoreCase))
                {
                    results.Add(kvp.Key);
                }
            }
            
            return results;
        }

        /// <summary>
        /// 加载所有游戏物品
        /// </summary>
        public async Task<List<GameItem>> LoadAllGameItemsAsync()
        {
            var items = new List<GameItem>();
            
            var xmlFiles = Directory.GetFiles(_dataDirectory, "client_items*.xml");
            
            foreach (var file in xmlFiles)
            {
                try
                {
                    var fileItems = await LoadGameItemsFromFileAsync(file);
                    items.AddRange(fileItems);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"加载物品文件失败: {file}, 错误: {ex.Message}");
                }
            }
            
            // 设置显示名称
            foreach (var item in items)
            {
                item.DisplayName = GetString(item.Description);
            }
            
            return items;
        }

        /// <summary>
        /// 从单个文件加载游戏物品
        /// </summary>
        private async Task<List<GameItem>> LoadGameItemsFromFileAsync(string filePath)
        {
            var items = new List<GameItem>();
            
            try
            {
                var content = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
                var doc = new XmlDocument();
                doc.LoadXml(content);
                
                var itemNodes = doc.SelectNodes("//client_item");
                if (itemNodes != null)
                {
                    foreach (XmlNode node in itemNodes)
                    {
                        try
                        {
                            var item = ParseGameItem(node);
                            item.SourceFile = filePath;
                            items.Add(item);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"解析物品节点失败: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取物品文件失败: {filePath}, 错误: {ex.Message}");
            }
            
            return items;
        }

        /// <summary>
        /// 解析单个游戏物品节点
        /// </summary>
        private GameItem ParseGameItem(XmlNode node)
        {
            var item = new GameItem();
            
            foreach (XmlNode child in node.ChildNodes)
            {
                if (child.NodeType != XmlNodeType.Element)
                    continue;
                    
                var value = child.InnerText?.Trim() ?? string.Empty;
                
                switch (child.Name.ToLower())
                {
                    case "id":
                        if (int.TryParse(value, out var id)) item.Id = id;
                        break;
                    case "name":
                        item.Name = value;
                        break;
                    case "desc":
                        item.Description = value;
                        break;
                    case "weapon_type":
                        item.WeaponType = value;
                        break;
                    case "item_type":
                        item.ItemType = value;
                        break;
                    case "mesh":
                        item.Mesh = value;
                        break;
                    case "mesh_change":
                        if (int.TryParse(value, out var meshChange)) item.MeshChange = meshChange;
                        break;
                    case "material":
                        item.Material = value;
                        break;
                    case "dmg_decal":
                        if (int.TryParse(value, out var dmgDecal)) item.DamageDecal = dmgDecal;
                        break;
                    case "icon_name":
                        item.IconName = value;
                        break;
                    case "blade_fx":
                        if (int.TryParse(value, out var bladeFx)) item.BladeFx = bladeFx;
                        break;
                    case "trail_tex":
                        item.TrailTexture = value;
                        break;
                    case "equip_bone":
                        item.EquipBone = value;
                        break;
                    case "combat_equip_bone":
                        item.CombatEquipBone = value;
                        break;
                    case "price":
                        if (int.TryParse(value, out var price)) item.Price = price;
                        break;
                    case "max_stack_count":
                        if (int.TryParse(value, out var maxStack)) item.MaxStackCount = maxStack;
                        break;
                    case "equipment_slots":
                        item.EquipmentSlots = value;
                        break;
                    case "min_damage":
                        if (int.TryParse(value, out var minDmg)) item.MinDamage = minDmg;
                        break;
                    case "max_damage":
                        if (int.TryParse(value, out var maxDmg)) item.MaxDamage = maxDmg;
                        break;
                    case "str":
                        if (int.TryParse(value, out var str)) item.Strength = str;
                        break;
                    case "agi":
                        if (int.TryParse(value, out var agi)) item.Agility = agi;
                        break;
                    case "kno":
                        if (int.TryParse(value, out var kno)) item.Knowledge = kno;
                        break;
                    case "vit":
                        if (int.TryParse(value, out var vit)) item.Vitality = vit;
                        break;
                    case "dex":
                        if (int.TryParse(value, out var dex)) item.Dexterity = dex;
                        break;
                    case "wil":
                        if (int.TryParse(value, out var wil)) item.Will = wil;
                        break;
                    case "hit_accuracy":
                        if (int.TryParse(value, out var hitAcc)) item.HitAccuracy = hitAcc;
                        break;
                    case "critical":
                        if (int.TryParse(value, out var crit)) item.Critical = crit;
                        break;
                    case "parry":
                        if (int.TryParse(value, out var parry)) item.Parry = parry;
                        break;
                    case "block":
                        if (int.TryParse(value, out var block)) item.Block = block;
                        break;
                    case "dodge":
                        if (int.TryParse(value, out var dodge)) item.Dodge = dodge;
                        break;
                    case "quality":
                        item.Quality = value;
                        break;
                    case "level":
                        if (int.TryParse(value, out var level)) item.Level = level;
                        break;
                    case "attack_type":
                        item.AttackType = value;
                        break;
                    case "attack_delay":
                        if (int.TryParse(value, out var attackDelay)) item.AttackDelay = attackDelay;
                        break;
                    case "hit_count":
                        if (int.TryParse(value, out var hitCount)) item.HitCount = hitCount;
                        break;
                    case "attack_gap":
                        if (float.TryParse(value, out var attackGap)) item.AttackGap = attackGap;
                        break;
                    case "attack_range":
                        if (float.TryParse(value, out var attackRange)) item.AttackRange = attackRange;
                        break;
                    // 布尔值属性
                    case "lore":
                        item.Lore = value.Equals("TRUE", StringComparison.OrdinalIgnoreCase);
                        break;
                    case "can_exchange":
                        item.CanExchange = value.Equals("TRUE", StringComparison.OrdinalIgnoreCase);
                        break;
                    case "can_sell_to_npc":
                        item.CanSellToNpc = value.Equals("TRUE", StringComparison.OrdinalIgnoreCase);
                        break;
                    case "breakable":
                        item.Breakable = value.Equals("TRUE", StringComparison.OrdinalIgnoreCase);
                        break;
                    case "soul_bind":
                        item.SoulBind = value.Equals("TRUE", StringComparison.OrdinalIgnoreCase);
                        break;
                    // 职业限制
                    case "warrior":
                        if (int.TryParse(value, out var warrior)) item.Warrior = warrior;
                        break;
                    case "scout":
                        if (int.TryParse(value, out var scout)) item.Scout = scout;
                        break;
                    case "mage":
                        if (int.TryParse(value, out var mage)) item.Mage = mage;
                        break;
                    case "cleric":
                        if (int.TryParse(value, out var cleric)) item.Cleric = cleric;
                        break;
                    case "engineer":
                        if (int.TryParse(value, out var engineer)) item.Engineer = engineer;
                        break;
                    case "artist":
                        if (int.TryParse(value, out var artist)) item.Artist = artist;
                        break;
                    case "fighter":
                        if (int.TryParse(value, out var fighter)) item.Fighter = fighter;
                        break;
                    case "knight":
                        if (int.TryParse(value, out var knight)) item.Knight = knight;
                        break;
                    case "assassin":
                        if (int.TryParse(value, out var assassin)) item.Assassin = assassin;
                        break;
                    case "ranger":
                        if (int.TryParse(value, out var ranger)) item.Ranger = ranger;
                        break;
                    case "wizard":
                        if (int.TryParse(value, out var wizard)) item.Wizard = wizard;
                        break;
                    case "elementalist":
                        if (int.TryParse(value, out var elementalist)) item.Elementalist = elementalist;
                        break;
                    case "chanter":
                        if (int.TryParse(value, out var chanter)) item.Chanter = chanter;
                        break;
                    case "priest":
                        if (int.TryParse(value, out var priest)) item.Priest = priest;
                        break;
                    case "gunner":
                        if (int.TryParse(value, out var gunner)) item.Gunner = gunner;
                        break;
                    case "bard":
                        if (int.TryParse(value, out var bard)) item.Bard = bard;
                        break;
                    case "rider":
                        if (int.TryParse(value, out var rider)) item.Rider = rider;
                        break;
                    // 其他属性
                    case "gender_permitted":
                        item.GenderPermitted = value;
                        break;
                    case "race_permitted":
                        item.RacePermitted = value;
                        break;
                }
            }
            
            return item;
        }
    }
}
