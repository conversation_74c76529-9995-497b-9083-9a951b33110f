using GameItemEditor.Models;

namespace GameItemEditor.Services
{
    /// <summary>
    /// 游戏数据管理服务
    /// 负责管理所有游戏物品数据和字符串映射
    /// </summary>
    public class GameDataService
    {
        private readonly XmlParserService _xmlParser;
        private List<GameItem> _allItems;
        private List<GameItem> _filteredItems;

        public GameDataService(string dataDirectory)
        {
            _xmlParser = new XmlParserService(dataDirectory);
            _allItems = new List<GameItem>();
            _filteredItems = new List<GameItem>();
        }

        /// <summary>
        /// 初始化数据服务
        /// </summary>
        public async Task InitializeAsync()
        {
            // 加载字符串映射
            await _xmlParser.LoadStringMappingsAsync();
            
            // 加载所有游戏物品
            _allItems = await _xmlParser.LoadAllGameItemsAsync();
            _filteredItems = new List<GameItem>(_allItems);
            
            Console.WriteLine($"已加载 {_allItems.Count} 个游戏物品");
        }

        /// <summary>
        /// 获取所有物品
        /// </summary>
        public List<GameItem> GetAllItems()
        {
            return new List<GameItem>(_allItems);
        }

        /// <summary>
        /// 获取过滤后的物品列表
        /// </summary>
        public List<GameItem> GetFilteredItems()
        {
            return new List<GameItem>(_filteredItems);
        }

        /// <summary>
        /// 根据中文名称搜索物品
        /// </summary>
        public List<GameItem> SearchByChineseName(string chineseText)
        {
            if (string.IsNullOrWhiteSpace(chineseText))
            {
                _filteredItems = new List<GameItem>(_allItems);
                return _filteredItems;
            }

            var results = new List<GameItem>();
            
            // 直接在显示名称中搜索
            var directMatches = _allItems.Where(item => 
                item.DisplayName.Contains(chineseText, StringComparison.OrdinalIgnoreCase))
                .ToList();
            
            results.AddRange(directMatches);
            
            // 在字符串映射中搜索相关的键
            var stringKeys = _xmlParser.SearchStringKeys(chineseText);
            
            foreach (var key in stringKeys)
            {
                var keyMatches = _allItems.Where(item => 
                    item.Description.Equals(key, StringComparison.OrdinalIgnoreCase))
                    .ToList();
                
                foreach (var match in keyMatches)
                {
                    if (!results.Any(r => r.Id == match.Id && r.SourceFile == match.SourceFile))
                    {
                        results.Add(match);
                    }
                }
            }
            
            _filteredItems = results;
            return results;
        }

        /// <summary>
        /// 根据物品ID搜索
        /// </summary>
        public List<GameItem> SearchById(int id)
        {
            var results = _allItems.Where(item => item.Id == id).ToList();
            _filteredItems = results;
            return results;
        }

        /// <summary>
        /// 根据物品名称搜索
        /// </summary>
        public List<GameItem> SearchByName(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                _filteredItems = new List<GameItem>(_allItems);
                return _filteredItems;
            }

            var results = _allItems.Where(item => 
                item.Name.Contains(name, StringComparison.OrdinalIgnoreCase) ||
                item.DisplayName.Contains(name, StringComparison.OrdinalIgnoreCase))
                .ToList();
            
            _filteredItems = results;
            return results;
        }

        /// <summary>
        /// 根据物品类型过滤
        /// </summary>
        public List<GameItem> FilterByType(string itemType, string weaponType = "")
        {
            var query = _allItems.AsQueryable();
            
            if (!string.IsNullOrWhiteSpace(itemType))
            {
                query = query.Where(item => item.ItemType.Equals(itemType, StringComparison.OrdinalIgnoreCase));
            }
            
            if (!string.IsNullOrWhiteSpace(weaponType))
            {
                query = query.Where(item => item.WeaponType.Equals(weaponType, StringComparison.OrdinalIgnoreCase));
            }
            
            _filteredItems = query.ToList();
            return _filteredItems;
        }

        /// <summary>
        /// 根据品质过滤
        /// </summary>
        public List<GameItem> FilterByQuality(string quality)
        {
            if (string.IsNullOrWhiteSpace(quality))
            {
                _filteredItems = new List<GameItem>(_allItems);
                return _filteredItems;
            }

            var results = _allItems.Where(item => 
                item.Quality.Equals(quality, StringComparison.OrdinalIgnoreCase))
                .ToList();
            
            _filteredItems = results;
            return results;
        }

        /// <summary>
        /// 根据等级范围过滤
        /// </summary>
        public List<GameItem> FilterByLevelRange(int minLevel, int maxLevel)
        {
            var results = _allItems.Where(item => 
                item.Level >= minLevel && item.Level <= maxLevel)
                .ToList();
            
            _filteredItems = results;
            return results;
        }

        /// <summary>
        /// 组合搜索
        /// </summary>
        public List<GameItem> CombinedSearch(string searchText, string itemType = "", 
            string weaponType = "", string quality = "", int minLevel = 0, int maxLevel = 999)
        {
            var query = _allItems.AsQueryable();
            
            // 文本搜索
            if (!string.IsNullOrWhiteSpace(searchText))
            {
                // 如果是数字，按ID搜索
                if (int.TryParse(searchText, out var id))
                {
                    query = query.Where(item => item.Id == id);
                }
                else
                {
                    // 否则按名称和中文名搜索
                    query = query.Where(item => 
                        item.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                        item.DisplayName.Contains(searchText, StringComparison.OrdinalIgnoreCase));
                }
            }
            
            // 类型过滤
            if (!string.IsNullOrWhiteSpace(itemType))
            {
                query = query.Where(item => item.ItemType.Equals(itemType, StringComparison.OrdinalIgnoreCase));
            }
            
            if (!string.IsNullOrWhiteSpace(weaponType))
            {
                query = query.Where(item => item.WeaponType.Equals(weaponType, StringComparison.OrdinalIgnoreCase));
            }
            
            // 品质过滤
            if (!string.IsNullOrWhiteSpace(quality))
            {
                query = query.Where(item => item.Quality.Equals(quality, StringComparison.OrdinalIgnoreCase));
            }
            
            // 等级过滤
            query = query.Where(item => item.Level >= minLevel && item.Level <= maxLevel);
            
            _filteredItems = query.ToList();
            return _filteredItems;
        }

        /// <summary>
        /// 获取所有物品类型
        /// </summary>
        public List<string> GetAllItemTypes()
        {
            return _allItems.Select(item => item.ItemType)
                .Where(type => !string.IsNullOrWhiteSpace(type))
                .Distinct()
                .OrderBy(type => type)
                .ToList();
        }

        /// <summary>
        /// 获取所有武器类型
        /// </summary>
        public List<string> GetAllWeaponTypes()
        {
            return _allItems.Select(item => item.WeaponType)
                .Where(type => !string.IsNullOrWhiteSpace(type))
                .Distinct()
                .OrderBy(type => type)
                .ToList();
        }

        /// <summary>
        /// 获取所有品质类型
        /// </summary>
        public List<string> GetAllQualities()
        {
            return _allItems.Select(item => item.Quality)
                .Where(quality => !string.IsNullOrWhiteSpace(quality))
                .Distinct()
                .OrderBy(quality => quality)
                .ToList();
        }

        /// <summary>
        /// 获取字符串映射
        /// </summary>
        public string GetString(string key)
        {
            return _xmlParser.GetString(key);
        }

        /// <summary>
        /// 根据ID获取物品
        /// </summary>
        public GameItem? GetItemById(int id, string sourceFile = "")
        {
            if (string.IsNullOrWhiteSpace(sourceFile))
            {
                return _allItems.FirstOrDefault(item => item.Id == id);
            }
            else
            {
                return _allItems.FirstOrDefault(item => item.Id == id && item.SourceFile == sourceFile);
            }
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        public Dictionary<string, int> GetStatistics()
        {
            var stats = new Dictionary<string, int>
            {
                ["总物品数"] = _allItems.Count,
                ["当前显示"] = _filteredItems.Count
            };
            
            // 按类型统计
            var typeGroups = _allItems.GroupBy(item => item.ItemType)
                .Where(g => !string.IsNullOrWhiteSpace(g.Key))
                .OrderByDescending(g => g.Count());
            
            foreach (var group in typeGroups.Take(5))
            {
                stats[$"{group.Key}类型"] = group.Count();
            }
            
            return stats;
        }
    }
}
