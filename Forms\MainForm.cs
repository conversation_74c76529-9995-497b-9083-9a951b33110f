using GameItemEditor.Models;
using GameItemEditor.Services;
using GameItemEditor.Controls;
using System.ComponentModel;

namespace GameItemEditor.Forms
{
    /// <summary>
    /// 主窗体
    /// 游戏物品编辑器的主界面
    /// </summary>
    public partial class MainForm : Form
    {
        private GameDataService? _gameDataService;
        private SearchService? _searchService;
        private GameItem? _selectedItem;
        private bool _isLoading = false;

        // 控件声明
        private MenuStrip menuStrip;
        private ToolStripMenuItem fileMenu;
        private ToolStripMenuItem openFolderMenuItem;
        private ToolStripMenuItem saveMenuItem;
        private ToolStripMenuItem exitMenuItem;
        
        private ToolStripMenuItem editMenu;
        private ToolStripMenuItem addItemMenuItem;
        private ToolStripMenuItem deleteItemMenuItem;
        private ToolStripMenuItem copyItemMenuItem;
        
        private ToolStripMenuItem helpMenu;
        private ToolStripMenuItem aboutMenuItem;
        
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;
        private ToolStripProgressBar progressBar;
        
        private SplitContainer mainSplitContainer;
        private SplitContainer leftSplitContainer;
        
        // 搜索区域
        private GroupBox searchGroupBox;
        private TextBox searchTextBox;
        private Button searchButton;
        private Button clearButton;
        private ComboBox searchTypeComboBox;
        private CheckBox fuzzySearchCheckBox;
        
        // 过滤区域
        private GroupBox filterGroupBox;
        private ComboBox itemTypeComboBox;
        private ComboBox weaponTypeComboBox;
        private ComboBox qualityComboBox;
        private NumericUpDown minLevelNumeric;
        private NumericUpDown maxLevelNumeric;
        private Button applyFilterButton;
        private Button resetFilterButton;
        
        // 结果列表
        private GroupBox resultsGroupBox;
        private ListView itemsListView;
        private Label resultCountLabel;
        
        // 详细信息编辑区域
        private GroupBox detailsGroupBox;
        private TabControl detailsTabControl;
        
        // 基础信息标签页
        private TabPage basicTabPage;
        private TableLayoutPanel basicTableLayout;
        
        // 属性标签页
        private TabPage attributesTabPage;
        private TableLayoutPanel attributesTableLayout;
        
        // 职业限制标签页
        private TabPage classesTabPage;
        private TableLayoutPanel classesTableLayout;
        
        // 其他设置标签页
        private TabPage otherTabPage;
        private TableLayoutPanel otherTableLayout;

        // 详细信息控件
        private ItemDetailsControl itemDetailsControl;

        public MainForm()
        {
            InitializeComponent();
            InitializeCustomComponents();
            SetupEventHandlers();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // 窗体设置
            this.Text = "游戏物品编辑器 - Game Item Editor";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(1200, 700);
            this.WindowState = FormWindowState.Maximized;
            
            this.ResumeLayout(false);
        }

        private void InitializeCustomComponents()
        {
            // 创建菜单栏
            CreateMenuStrip();
            
            // 创建状态栏
            CreateStatusStrip();
            
            // 创建主布局
            CreateMainLayout();
            
            // 创建搜索区域
            CreateSearchArea();
            
            // 创建过滤区域
            CreateFilterArea();
            
            // 创建结果列表
            CreateResultsList();
            
            // 创建详细信息编辑区域
            CreateDetailsArea();
        }

        private void CreateMenuStrip()
        {
            menuStrip = new MenuStrip();
            
            // 文件菜单
            fileMenu = new ToolStripMenuItem("文件(&F)");
            openFolderMenuItem = new ToolStripMenuItem("打开数据文件夹(&O)...", null, OpenFolder_Click);
            openFolderMenuItem.ShortcutKeys = Keys.Control | Keys.O;
            saveMenuItem = new ToolStripMenuItem("保存(&S)", null, Save_Click);
            saveMenuItem.ShortcutKeys = Keys.Control | Keys.S;
            saveMenuItem.Enabled = false;
            exitMenuItem = new ToolStripMenuItem("退出(&X)", null, Exit_Click);
            
            fileMenu.DropDownItems.AddRange(new ToolStripItem[] {
                openFolderMenuItem,
                new ToolStripSeparator(),
                saveMenuItem,
                new ToolStripSeparator(),
                exitMenuItem
            });
            
            // 编辑菜单
            editMenu = new ToolStripMenuItem("编辑(&E)");
            addItemMenuItem = new ToolStripMenuItem("添加物品(&A)...", null, AddItem_Click);
            addItemMenuItem.Enabled = false;
            deleteItemMenuItem = new ToolStripMenuItem("删除物品(&D)", null, DeleteItem_Click);
            deleteItemMenuItem.Enabled = false;
            copyItemMenuItem = new ToolStripMenuItem("复制物品(&C)", null, CopyItem_Click);
            copyItemMenuItem.Enabled = false;
            
            editMenu.DropDownItems.AddRange(new ToolStripItem[] {
                addItemMenuItem,
                deleteItemMenuItem,
                copyItemMenuItem
            });
            
            // 帮助菜单
            helpMenu = new ToolStripMenuItem("帮助(&H)");
            aboutMenuItem = new ToolStripMenuItem("关于(&A)...", null, About_Click);
            helpMenu.DropDownItems.Add(aboutMenuItem);
            
            menuStrip.Items.AddRange(new ToolStripItem[] {
                fileMenu, editMenu, helpMenu
            });
            
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip();
            statusLabel = new ToolStripStatusLabel("就绪");
            statusLabel.Spring = true;
            statusLabel.TextAlign = ContentAlignment.MiddleLeft;
            
            progressBar = new ToolStripProgressBar();
            progressBar.Visible = false;
            
            statusStrip.Items.AddRange(new ToolStripItem[] {
                statusLabel, progressBar
            });
            
            this.Controls.Add(statusStrip);
        }

        private void CreateMainLayout()
        {
            mainSplitContainer = new SplitContainer();
            mainSplitContainer.Dock = DockStyle.Fill;
            mainSplitContainer.SplitterDistance = 400;
            mainSplitContainer.FixedPanel = FixedPanel.Panel1;
            
            leftSplitContainer = new SplitContainer();
            leftSplitContainer.Dock = DockStyle.Fill;
            leftSplitContainer.Orientation = Orientation.Horizontal;
            leftSplitContainer.SplitterDistance = 200;
            leftSplitContainer.FixedPanel = FixedPanel.Panel1;
            
            mainSplitContainer.Panel1.Controls.Add(leftSplitContainer);
            this.Controls.Add(mainSplitContainer);
        }

        private void CreateSearchArea()
        {
            searchGroupBox = new GroupBox();
            searchGroupBox.Text = "搜索";
            searchGroupBox.Dock = DockStyle.Fill;
            searchGroupBox.Padding = new Padding(10);
            
            var searchLayout = new TableLayoutPanel();
            searchLayout.Dock = DockStyle.Fill;
            searchLayout.ColumnCount = 4;
            searchLayout.RowCount = 3;
            searchLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 60F));
            searchLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 15F));
            searchLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 15F));
            searchLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 10F));
            
            // 搜索框
            var searchLabel = new Label { Text = "搜索内容:", Anchor = AnchorStyles.Left };
            searchTextBox = new TextBox { Dock = DockStyle.Fill };
            searchTextBox.PlaceholderText = "输入物品名称、ID或中文名称...";
            
            searchButton = new Button { Text = "搜索", Dock = DockStyle.Fill };
            clearButton = new Button { Text = "清空", Dock = DockStyle.Fill };
            
            // 搜索类型
            var typeLabel = new Label { Text = "搜索类型:", Anchor = AnchorStyles.Left };
            searchTypeComboBox = new ComboBox { Dock = DockStyle.Fill };
            searchTypeComboBox.Items.AddRange(new[] { "智能搜索", "精确搜索", "模糊搜索" });
            searchTypeComboBox.SelectedIndex = 0;
            
            fuzzySearchCheckBox = new CheckBox { Text = "启用模糊匹配", Dock = DockStyle.Fill };
            
            searchLayout.Controls.Add(searchLabel, 0, 0);
            searchLayout.Controls.Add(searchTextBox, 0, 1);
            searchLayout.Controls.Add(searchButton, 1, 1);
            searchLayout.Controls.Add(clearButton, 2, 1);
            
            searchLayout.Controls.Add(typeLabel, 0, 2);
            searchLayout.Controls.Add(searchTypeComboBox, 1, 2);
            searchLayout.Controls.Add(fuzzySearchCheckBox, 2, 2);
            
            searchGroupBox.Controls.Add(searchLayout);
            leftSplitContainer.Panel1.Controls.Add(searchGroupBox);
        }

        private void CreateFilterArea()
        {
            filterGroupBox = new GroupBox();
            filterGroupBox.Text = "过滤条件";
            filterGroupBox.Height = 120;
            filterGroupBox.Dock = DockStyle.Top;
            filterGroupBox.Padding = new Padding(10);
            
            var filterLayout = new TableLayoutPanel();
            filterLayout.Dock = DockStyle.Fill;
            filterLayout.ColumnCount = 4;
            filterLayout.RowCount = 3;
            
            // 物品类型
            var itemTypeLabel = new Label { Text = "物品类型:", Anchor = AnchorStyles.Left };
            itemTypeComboBox = new ComboBox { Dock = DockStyle.Fill, DropDownStyle = ComboBoxStyle.DropDownList };
            
            // 武器类型
            var weaponTypeLabel = new Label { Text = "武器类型:", Anchor = AnchorStyles.Left };
            weaponTypeComboBox = new ComboBox { Dock = DockStyle.Fill, DropDownStyle = ComboBoxStyle.DropDownList };
            
            // 品质
            var qualityLabel = new Label { Text = "品质:", Anchor = AnchorStyles.Left };
            qualityComboBox = new ComboBox { Dock = DockStyle.Fill, DropDownStyle = ComboBoxStyle.DropDownList };
            
            // 等级范围
            var levelLabel = new Label { Text = "等级范围:", Anchor = AnchorStyles.Left };
            var levelPanel = new Panel { Dock = DockStyle.Fill };
            minLevelNumeric = new NumericUpDown { Minimum = 0, Maximum = 999, Value = 0, Width = 60 };
            var toLabel = new Label { Text = " - ", AutoSize = true };
            maxLevelNumeric = new NumericUpDown { Minimum = 0, Maximum = 999, Value = 999, Width = 60 };
            
            toLabel.Location = new Point(minLevelNumeric.Right + 5, minLevelNumeric.Top + 3);
            maxLevelNumeric.Location = new Point(toLabel.Right + 5, minLevelNumeric.Top);
            
            levelPanel.Controls.AddRange(new Control[] { minLevelNumeric, toLabel, maxLevelNumeric });
            
            // 按钮
            applyFilterButton = new Button { Text = "应用过滤", Dock = DockStyle.Fill };
            resetFilterButton = new Button { Text = "重置", Dock = DockStyle.Fill };
            
            filterLayout.Controls.Add(itemTypeLabel, 0, 0);
            filterLayout.Controls.Add(itemTypeComboBox, 1, 0);
            filterLayout.Controls.Add(weaponTypeLabel, 2, 0);
            filterLayout.Controls.Add(weaponTypeComboBox, 3, 0);
            
            filterLayout.Controls.Add(qualityLabel, 0, 1);
            filterLayout.Controls.Add(qualityComboBox, 1, 1);
            filterLayout.Controls.Add(levelLabel, 2, 1);
            filterLayout.Controls.Add(levelPanel, 3, 1);
            
            filterLayout.Controls.Add(applyFilterButton, 2, 2);
            filterLayout.Controls.Add(resetFilterButton, 3, 2);
            
            filterGroupBox.Controls.Add(filterLayout);
            leftSplitContainer.Panel2.Controls.Add(filterGroupBox);
        }

        private void CreateResultsList()
        {
            resultsGroupBox = new GroupBox();
            resultsGroupBox.Text = "搜索结果";
            resultsGroupBox.Dock = DockStyle.Fill;
            resultsGroupBox.Padding = new Padding(10);
            
            resultCountLabel = new Label();
            resultCountLabel.Text = "共 0 个物品";
            resultCountLabel.Dock = DockStyle.Top;
            resultCountLabel.Height = 25;
            resultCountLabel.TextAlign = ContentAlignment.MiddleLeft;
            
            itemsListView = new ListView();
            itemsListView.Dock = DockStyle.Fill;
            itemsListView.View = View.Details;
            itemsListView.FullRowSelect = true;
            itemsListView.GridLines = true;
            itemsListView.MultiSelect = false;
            itemsListView.HideSelection = false;
            
            // 添加列
            itemsListView.Columns.Add("ID", 80);
            itemsListView.Columns.Add("名称", 150);
            itemsListView.Columns.Add("中文名", 200);
            itemsListView.Columns.Add("类型", 100);
            itemsListView.Columns.Add("武器类型", 100);
            itemsListView.Columns.Add("品质", 80);
            itemsListView.Columns.Add("等级", 60);
            itemsListView.Columns.Add("价格", 80);
            
            resultsGroupBox.Controls.Add(itemsListView);
            resultsGroupBox.Controls.Add(resultCountLabel);
            
            leftSplitContainer.Panel2.Controls.Add(resultsGroupBox);
        }

        private void CreateDetailsArea()
        {
            detailsGroupBox = new GroupBox();
            detailsGroupBox.Text = "物品详细信息";
            detailsGroupBox.Dock = DockStyle.Fill;
            detailsGroupBox.Padding = new Padding(10);

            detailsTabControl = new TabControl();
            detailsTabControl.Dock = DockStyle.Fill;

            // 创建详细信息控件
            itemDetailsControl = new ItemDetailsControl();
            itemDetailsControl.ItemChanged += ItemDetailsControl_ItemChanged;
            itemDetailsControl.ItemSaved += ItemDetailsControl_ItemSaved;

            // 创建标签页
            CreateBasicTabPage();
            CreateAttributesTabPage();
            CreateClassesTabPage();
            CreateOtherTabPage();

            detailsTabControl.TabPages.AddRange(new TabPage[] {
                basicTabPage, attributesTabPage, classesTabPage, otherTabPage
            });

            detailsGroupBox.Controls.Add(detailsTabControl);
            mainSplitContainer.Panel2.Controls.Add(detailsGroupBox);
        }

        private void CreateBasicTabPage()
        {
            basicTabPage = new TabPage("基础信息");
            basicTableLayout = new TableLayoutPanel();
            basicTableLayout.Dock = DockStyle.Fill;
            basicTableLayout.ColumnCount = 4;
            basicTableLayout.AutoScroll = true;
            basicTableLayout.Padding = new Padding(10);

            // 让详细信息控件创建基础控件
            itemDetailsControl.CreateBasicControls(basicTableLayout);

            basicTabPage.Controls.Add(basicTableLayout);
        }

        private void CreateAttributesTabPage()
        {
            attributesTabPage = new TabPage("属性");
            attributesTableLayout = new TableLayoutPanel();
            attributesTableLayout.Dock = DockStyle.Fill;
            attributesTableLayout.ColumnCount = 4;
            attributesTableLayout.AutoScroll = true;
            attributesTableLayout.Padding = new Padding(10);
            
            attributesTabPage.Controls.Add(attributesTableLayout);
        }

        private void CreateClassesTabPage()
        {
            classesTabPage = new TabPage("职业限制");
            classesTableLayout = new TableLayoutPanel();
            classesTableLayout.Dock = DockStyle.Fill;
            classesTableLayout.ColumnCount = 4;
            classesTableLayout.AutoScroll = true;
            classesTableLayout.Padding = new Padding(10);
            
            classesTabPage.Controls.Add(classesTableLayout);
        }

        private void CreateOtherTabPage()
        {
            otherTabPage = new TabPage("其他设置");
            otherTableLayout = new TableLayoutPanel();
            otherTableLayout.Dock = DockStyle.Fill;
            otherTableLayout.ColumnCount = 4;
            otherTableLayout.AutoScroll = true;
            otherTableLayout.Padding = new Padding(10);
            
            otherTabPage.Controls.Add(otherTableLayout);
        }

        private void SetupEventHandlers()
        {
            // 搜索相关事件
            searchButton.Click += SearchButton_Click;
            clearButton.Click += ClearButton_Click;
            searchTextBox.KeyDown += SearchTextBox_KeyDown;
            
            // 过滤相关事件
            applyFilterButton.Click += ApplyFilterButton_Click;
            resetFilterButton.Click += ResetFilterButton_Click;
            
            // 列表选择事件
            itemsListView.SelectedIndexChanged += ItemsListView_SelectedIndexChanged;
            
            // 窗体事件
            this.Load += MainForm_Load;
            this.FormClosing += MainForm_FormClosing;
        }

        #region 事件处理方法

        private async void MainForm_Load(object sender, EventArgs e)
        {
            statusLabel.Text = "程序启动完成，请选择数据文件夹";

            // 显示欢迎信息
            MessageBox.Show(
                "欢迎使用游戏物品编辑器！\n\n" +
                "使用说明：\n" +
                "1. 点击 文件 -> 打开数据文件夹，选择包含XML文件的文件夹\n" +
                "2. 在搜索框中输入物品名称、ID或中文名称进行搜索\n" +
                "3. 支持模糊搜索和高级过滤功能\n" +
                "4. 选择物品后可在右侧编辑详细信息\n" +
                "5. 修改后记得保存更改",
                "游戏物品编辑器",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 检查是否有未保存的更改
            if (HasUnsavedChanges())
            {
                var result = MessageBox.Show(
                    "您有未保存的更改，是否要保存？",
                    "确认退出",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // 保存更改
                    Save_Click(sender, e);
                }
                else if (result == DialogResult.Cancel)
                {
                    e.Cancel = true;
                    return;
                }
            }
        }

        private async void OpenFolder_Click(object sender, EventArgs e)
        {
            using var folderDialog = new FolderBrowserDialog();
            folderDialog.Description = "选择包含游戏数据XML文件的文件夹";
            folderDialog.UseDescriptionForTitle = true;

            if (folderDialog.ShowDialog() == DialogResult.OK)
            {
                await LoadGameData(folderDialog.SelectedPath);
            }
        }

        private void Save_Click(object sender, EventArgs e)
        {
            try
            {
                // TODO: 实现保存功能
                MessageBox.Show("保存功能将在后续版本中实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void Exit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void AddItem_Click(object sender, EventArgs e)
        {
            // TODO: 实现添加物品功能
            MessageBox.Show("添加物品功能将在后续版本中实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteItem_Click(object sender, EventArgs e)
        {
            if (_selectedItem == null)
            {
                MessageBox.Show("请先选择要删除的物品", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show(
                $"确定要删除物品 \"{_selectedItem.DisplayName}\" (ID: {_selectedItem.Id}) 吗？",
                "确认删除",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // TODO: 实现删除功能
                MessageBox.Show("删除功能将在后续版本中实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void CopyItem_Click(object sender, EventArgs e)
        {
            if (_selectedItem == null)
            {
                MessageBox.Show("请先选择要复制的物品", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // TODO: 实现复制物品功能
            MessageBox.Show("复制物品功能将在后续版本中实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void About_Click(object sender, EventArgs e)
        {
            MessageBox.Show(
                "游戏物品编辑器 v1.0\n\n" +
                "一个用于编辑游戏物品XML数据的工具\n" +
                "支持中文搜索、模糊匹配、批量编辑等功能\n\n" +
                "开发语言：C# .NET 6.0\n" +
                "界面框架：Windows Forms\n\n" +
                "© 2024 Game Item Editor",
                "关于",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        private async void SearchButton_Click(object sender, EventArgs e)
        {
            await PerformSearch();
        }

        private void ClearButton_Click(object sender, EventArgs e)
        {
            searchTextBox.Clear();
            ClearResults();
            ClearSelection();
        }

        private async void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                await PerformSearch();
            }
        }

        private async void ApplyFilterButton_Click(object sender, EventArgs e)
        {
            await ApplyFilters();
        }

        private void ResetFilterButton_Click(object sender, EventArgs e)
        {
            ResetFilters();
        }

        private void ItemsListView_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (itemsListView.SelectedItems.Count > 0)
            {
                var selectedIndex = itemsListView.SelectedItems[0].Tag as int?;
                if (selectedIndex.HasValue)
                {
                    var items = _gameDataService?.GetFilteredItems();
                    if (items != null && selectedIndex.Value < items.Count)
                    {
                        _selectedItem = items[selectedIndex.Value];
                        DisplayItemDetails(_selectedItem);

                        // 启用编辑菜单项
                        deleteItemMenuItem.Enabled = true;
                        copyItemMenuItem.Enabled = true;
                    }
                }
            }
            else
            {
                ClearSelection();
            }
        }

        #endregion

        #region 私有方法

        private async Task LoadGameData(string dataPath)
        {
            try
            {
                _isLoading = true;
                progressBar.Visible = true;
                statusLabel.Text = "正在加载游戏数据...";

                _gameDataService = new GameDataService(dataPath);
                await _gameDataService.InitializeAsync();

                _searchService = new SearchService(_gameDataService);

                // 初始化过滤器选项
                InitializeFilterOptions();

                // 显示所有物品
                var allItems = _gameDataService.GetAllItems();
                DisplaySearchResults(allItems, "显示所有物品");

                // 启用相关功能
                saveMenuItem.Enabled = true;
                addItemMenuItem.Enabled = true;

                statusLabel.Text = $"数据加载完成，共 {allItems.Count} 个物品";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载数据失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "数据加载失败";
            }
            finally
            {
                _isLoading = false;
                progressBar.Visible = false;
            }
        }

        private void InitializeFilterOptions()
        {
            if (_gameDataService == null) return;

            // 物品类型
            itemTypeComboBox.Items.Clear();
            itemTypeComboBox.Items.Add("全部");
            itemTypeComboBox.Items.AddRange(_gameDataService.GetAllItemTypes().ToArray());
            itemTypeComboBox.SelectedIndex = 0;

            // 武器类型
            weaponTypeComboBox.Items.Clear();
            weaponTypeComboBox.Items.Add("全部");
            weaponTypeComboBox.Items.AddRange(_gameDataService.GetAllWeaponTypes().ToArray());
            weaponTypeComboBox.SelectedIndex = 0;

            // 品质
            qualityComboBox.Items.Clear();
            qualityComboBox.Items.Add("全部");
            qualityComboBox.Items.AddRange(_gameDataService.GetAllQualities().ToArray());
            qualityComboBox.SelectedIndex = 0;
        }

        private async Task PerformSearch()
        {
            if (_searchService == null || _isLoading) return;

            try
            {
                var searchText = searchTextBox.Text.Trim();
                var searchType = searchTypeComboBox.SelectedItem?.ToString() ?? "智能搜索";

                statusLabel.Text = "正在搜索...";

                SearchResult result;

                switch (searchType)
                {
                    case "模糊搜索":
                        result = _searchService.FuzzySearch(searchText);
                        break;
                    case "精确搜索":
                        result = _searchService.SmartSearch(searchText);
                        break;
                    default:
                        result = _searchService.SmartSearch(searchText);
                        break;
                }

                DisplaySearchResults(result.Items, result.Message);
                statusLabel.Text = result.Message;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"搜索失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "搜索失败";
            }
        }

        private async Task ApplyFilters()
        {
            if (_gameDataService == null || _isLoading) return;

            try
            {
                var searchText = searchTextBox.Text.Trim();
                var itemType = itemTypeComboBox.SelectedItem?.ToString();
                var weaponType = weaponTypeComboBox.SelectedItem?.ToString();
                var quality = qualityComboBox.SelectedItem?.ToString();

                if (itemType == "全部") itemType = "";
                if (weaponType == "全部") weaponType = "";
                if (quality == "全部") quality = "";

                var minLevel = (int)minLevelNumeric.Value;
                var maxLevel = (int)maxLevelNumeric.Value;

                statusLabel.Text = "正在应用过滤条件...";

                var results = _gameDataService.CombinedSearch(
                    searchText, itemType, weaponType, quality, minLevel, maxLevel);

                var message = $"过滤结果：找到 {results.Count} 个物品";
                DisplaySearchResults(results, message);
                statusLabel.Text = message;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"过滤失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "过滤失败";
            }
        }

        private void ResetFilters()
        {
            itemTypeComboBox.SelectedIndex = 0;
            weaponTypeComboBox.SelectedIndex = 0;
            qualityComboBox.SelectedIndex = 0;
            minLevelNumeric.Value = 0;
            maxLevelNumeric.Value = 999;
        }

        private void DisplaySearchResults(List<GameItem> items, string message)
        {
            itemsListView.Items.Clear();

            for (int i = 0; i < items.Count; i++)
            {
                var item = items[i];
                var listItem = new ListViewItem(item.Id.ToString());
                listItem.SubItems.Add(item.Name);
                listItem.SubItems.Add(item.DisplayName);
                listItem.SubItems.Add(item.ItemType);
                listItem.SubItems.Add(item.WeaponType);
                listItem.SubItems.Add(item.Quality);
                listItem.SubItems.Add(item.Level.ToString());
                listItem.SubItems.Add(item.Price.ToString());
                listItem.Tag = i; // 存储索引

                itemsListView.Items.Add(listItem);
            }

            resultCountLabel.Text = $"共 {items.Count} 个物品";
        }

        private void ClearResults()
        {
            itemsListView.Items.Clear();
            resultCountLabel.Text = "共 0 个物品";
        }

        private void ClearSelection()
        {
            _selectedItem = null;
            ClearItemDetails();

            // 禁用编辑菜单项
            deleteItemMenuItem.Enabled = false;
            copyItemMenuItem.Enabled = false;
        }

        private void DisplayItemDetails(GameItem item)
        {
            // TODO: 实现详细信息显示
            // 这将在下一个任务中实现
        }

        private void ClearItemDetails()
        {
            // TODO: 清空详细信息显示
        }

        private bool HasUnsavedChanges()
        {
            // TODO: 检查是否有未保存的更改
            return false;
        }

        #endregion
    }
}
