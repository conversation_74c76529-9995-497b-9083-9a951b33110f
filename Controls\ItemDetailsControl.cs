using GameItemEditor.Models;
using System.ComponentModel;

namespace GameItemEditor.Controls
{
    /// <summary>
    /// 物品详细信息编辑控件
    /// </summary>
    public partial class ItemDetailsControl : UserControl
    {
        private GameItem? _currentItem;
        private bool _isUpdating = false;
        private bool _hasChanges = false;

        // 基础信息控件
        private Dictionary<string, Control> _basicControls = new Dictionary<string, Control>();
        
        // 属性控件
        private Dictionary<string, Control> _attributeControls = new Dictionary<string, Control>();
        
        // 职业限制控件
        private Dictionary<string, Control> _classControls = new Dictionary<string, Control>();
        
        // 其他设置控件
        private Dictionary<string, Control> _otherControls = new Dictionary<string, Control>();

        public event EventHandler? ItemChanged;
        public event EventHandler? ItemSaved;

        public GameItem? CurrentItem
        {
            get => _currentItem;
            set
            {
                _currentItem = value;
                UpdateDisplay();
            }
        }

        public bool HasChanges => _hasChanges;

        public ItemDetailsControl()
        {
            InitializeComponent();
            CreateDetailControls();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            this.AutoScroll = true;
            this.BackColor = SystemColors.Control;
            this.Padding = new Padding(10);
            
            this.ResumeLayout(false);
        }

        private void CreateDetailControls()
        {
            // 这个方法将创建所有编辑控件
            // 由于控件较多，我们将分别创建不同类别的控件
        }

        /// <summary>
        /// 创建基础信息编辑控件
        /// </summary>
        public void CreateBasicControls(TableLayoutPanel container)
        {
            container.Controls.Clear();
            container.RowCount = 0;
            container.RowStyles.Clear();
            _basicControls.Clear();

            var fields = new[]
            {
                ("ID", "id", typeof(NumericUpDown)),
                ("名称", "name", typeof(TextBox)),
                ("描述键", "desc", typeof(TextBox)),
                ("中文名", "displayName", typeof(TextBox)),
                ("物品类型", "itemType", typeof(ComboBox)),
                ("武器类型", "weaponType", typeof(ComboBox)),
                ("网格模型", "mesh", typeof(TextBox)),
                ("材质", "material", typeof(TextBox)),
                ("图标名称", "iconName", typeof(TextBox)),
                ("装备骨骼", "equipBone", typeof(TextBox)),
                ("战斗装备骨骼", "combatEquipBone", typeof(TextBox)),
                ("价格", "price", typeof(NumericUpDown)),
                ("最大堆叠数", "maxStackCount", typeof(NumericUpDown)),
                ("装备槽位", "equipmentSlots", typeof(TextBox)),
                ("品质", "quality", typeof(ComboBox)),
                ("等级", "level", typeof(NumericUpDown))
            };

            for (int i = 0; i < fields.Length; i++)
            {
                var (labelText, propertyName, controlType) = fields[i];
                
                container.RowCount++;
                container.RowStyles.Add(new RowStyle(SizeType.AutoSize));
                
                // 创建标签
                var label = new Label
                {
                    Text = labelText + ":",
                    Anchor = AnchorStyles.Left,
                    AutoSize = true,
                    Margin = new Padding(3, 6, 3, 3)
                };
                
                // 创建编辑控件
                Control editControl;
                
                if (controlType == typeof(NumericUpDown))
                {
                    var numeric = new NumericUpDown
                    {
                        Dock = DockStyle.Fill,
                        Minimum = propertyName == "id" ? 1 : 0,
                        Maximum = propertyName == "id" ? int.MaxValue : 999999,
                        Margin = new Padding(3)
                    };
                    numeric.ValueChanged += (s, e) => OnControlValueChanged(propertyName, numeric.Value);
                    editControl = numeric;
                }
                else if (controlType == typeof(ComboBox))
                {
                    var combo = new ComboBox
                    {
                        Dock = DockStyle.Fill,
                        DropDownStyle = ComboBoxStyle.DropDown,
                        Margin = new Padding(3)
                    };
                    
                    // 为特定字段添加预设选项
                    switch (propertyName)
                    {
                        case "itemType":
                            combo.Items.AddRange(new[] { "normal", "abyss", "draconic" });
                            break;
                        case "weaponType":
                            combo.Items.AddRange(new[] { "1h_sword", "2h_sword", "dagger", "mace", "bow", "staff", "orb", "book", "polearm" });
                            break;
                        case "quality":
                            combo.Items.AddRange(new[] { "common", "rare", "unique", "epic", "legendary" });
                            break;
                    }
                    
                    combo.TextChanged += (s, e) => OnControlValueChanged(propertyName, combo.Text);
                    editControl = combo;
                }
                else
                {
                    var textBox = new TextBox
                    {
                        Dock = DockStyle.Fill,
                        Margin = new Padding(3),
                        ReadOnly = propertyName == "displayName" // 中文名只读，从strings文件获取
                    };
                    textBox.TextChanged += (s, e) => OnControlValueChanged(propertyName, textBox.Text);
                    editControl = textBox;
                }
                
                _basicControls[propertyName] = editControl;
                
                container.Controls.Add(label, 0, i);
                container.Controls.Add(editControl, 1, i);
                
                // 如果是第二列，添加到第三列
                if (i % 2 == 1 && i + 1 < fields.Length)
                {
                    var (labelText2, propertyName2, controlType2) = fields[i + 1];
                    
                    var label2 = new Label
                    {
                        Text = labelText2 + ":",
                        Anchor = AnchorStyles.Left,
                        AutoSize = true,
                        Margin = new Padding(3, 6, 3, 3)
                    };
                    
                    Control editControl2;
                    
                    if (controlType2 == typeof(NumericUpDown))
                    {
                        var numeric2 = new NumericUpDown
                        {
                            Dock = DockStyle.Fill,
                            Minimum = propertyName2 == "id" ? 1 : 0,
                            Maximum = propertyName2 == "id" ? int.MaxValue : 999999,
                            Margin = new Padding(3)
                        };
                        numeric2.ValueChanged += (s, e) => OnControlValueChanged(propertyName2, numeric2.Value);
                        editControl2 = numeric2;
                    }
                    else if (controlType2 == typeof(ComboBox))
                    {
                        var combo2 = new ComboBox
                        {
                            Dock = DockStyle.Fill,
                            DropDownStyle = ComboBoxStyle.DropDown,
                            Margin = new Padding(3)
                        };
                        
                        switch (propertyName2)
                        {
                            case "itemType":
                                combo2.Items.AddRange(new[] { "normal", "abyss", "draconic" });
                                break;
                            case "weaponType":
                                combo2.Items.AddRange(new[] { "1h_sword", "2h_sword", "dagger", "mace", "bow", "staff", "orb", "book", "polearm" });
                                break;
                            case "quality":
                                combo2.Items.AddRange(new[] { "common", "rare", "unique", "epic", "legendary" });
                                break;
                        }
                        
                        combo2.TextChanged += (s, e) => OnControlValueChanged(propertyName2, combo2.Text);
                        editControl2 = combo2;
                    }
                    else
                    {
                        var textBox2 = new TextBox
                        {
                            Dock = DockStyle.Fill,
                            Margin = new Padding(3),
                            ReadOnly = propertyName2 == "displayName"
                        };
                        textBox2.TextChanged += (s, e) => OnControlValueChanged(propertyName2, textBox2.Text);
                        editControl2 = textBox2;
                    }
                    
                    _basicControls[propertyName2] = editControl2;
                    
                    container.Controls.Add(label2, 2, i);
                    container.Controls.Add(editControl2, 3, i);
                    
                    i++; // 跳过下一个，因为已经处理了
                }
            }
        }

        /// <summary>
        /// 创建属性编辑控件
        /// </summary>
        public void CreateAttributeControls(TableLayoutPanel container)
        {
            container.Controls.Clear();
            container.RowCount = 0;
            container.RowStyles.Clear();
            _attributeControls.Clear();

            var attributes = new[]
            {
                ("最小伤害", "minDamage"),
                ("最大伤害", "maxDamage"),
                ("力量", "strength"),
                ("敏捷", "agility"),
                ("知识", "knowledge"),
                ("体质", "vitality"),
                ("灵巧", "dexterity"),
                ("意志", "will"),
                ("命中", "hitAccuracy"),
                ("暴击", "critical"),
                ("格挡", "block"),
                ("招架", "parry"),
                ("闪避", "dodge"),
                ("魔法技能增强", "magicalSkillBoost"),
                ("魔法技能抗性", "magicalSkillBoostResist"),
                ("魔法命中", "magicalHitAccuracy"),
                ("攻击类型", "attackType"),
                ("攻击延迟", "attackDelay"),
                ("攻击次数", "hitCount"),
                ("攻击间隔", "attackGap"),
                ("攻击范围", "attackRange")
            };

            for (int i = 0; i < attributes.Length; i += 2)
            {
                container.RowCount++;
                container.RowStyles.Add(new RowStyle(SizeType.AutoSize));
                
                // 第一个属性
                var (labelText1, propertyName1) = attributes[i];
                var label1 = new Label
                {
                    Text = labelText1 + ":",
                    Anchor = AnchorStyles.Left,
                    AutoSize = true,
                    Margin = new Padding(3, 6, 3, 3)
                };
                
                Control control1;
                if (propertyName1 == "attackType")
                {
                    var combo1 = new ComboBox
                    {
                        Dock = DockStyle.Fill,
                        DropDownStyle = ComboBoxStyle.DropDown,
                        Margin = new Padding(3)
                    };
                    combo1.Items.AddRange(new[] { "physical", "magical" });
                    combo1.TextChanged += (s, e) => OnControlValueChanged(propertyName1, combo1.Text);
                    control1 = combo1;
                }
                else if (propertyName1 == "attackGap" || propertyName1 == "attackRange")
                {
                    var numeric1 = new NumericUpDown
                    {
                        Dock = DockStyle.Fill,
                        DecimalPlaces = 6,
                        Minimum = 0,
                        Maximum = 999999,
                        Margin = new Padding(3)
                    };
                    numeric1.ValueChanged += (s, e) => OnControlValueChanged(propertyName1, (float)numeric1.Value);
                    control1 = numeric1;
                }
                else
                {
                    var numeric1 = new NumericUpDown
                    {
                        Dock = DockStyle.Fill,
                        Minimum = 0,
                        Maximum = 999999,
                        Margin = new Padding(3)
                    };
                    numeric1.ValueChanged += (s, e) => OnControlValueChanged(propertyName1, (int)numeric1.Value);
                    control1 = numeric1;
                }
                
                _attributeControls[propertyName1] = control1;
                
                container.Controls.Add(label1, 0, i / 2);
                container.Controls.Add(control1, 1, i / 2);
                
                // 第二个属性（如果存在）
                if (i + 1 < attributes.Length)
                {
                    var (labelText2, propertyName2) = attributes[i + 1];
                    var label2 = new Label
                    {
                        Text = labelText2 + ":",
                        Anchor = AnchorStyles.Left,
                        AutoSize = true,
                        Margin = new Padding(3, 6, 3, 3)
                    };
                    
                    Control control2;
                    if (propertyName2 == "attackType")
                    {
                        var combo2 = new ComboBox
                        {
                            Dock = DockStyle.Fill,
                            DropDownStyle = ComboBoxStyle.DropDown,
                            Margin = new Padding(3)
                        };
                        combo2.Items.AddRange(new[] { "physical", "magical" });
                        combo2.TextChanged += (s, e) => OnControlValueChanged(propertyName2, combo2.Text);
                        control2 = combo2;
                    }
                    else if (propertyName2 == "attackGap" || propertyName2 == "attackRange")
                    {
                        var numeric2 = new NumericUpDown
                        {
                            Dock = DockStyle.Fill,
                            DecimalPlaces = 6,
                            Minimum = 0,
                            Maximum = 999999,
                            Margin = new Padding(3)
                        };
                        numeric2.ValueChanged += (s, e) => OnControlValueChanged(propertyName2, (float)numeric2.Value);
                        control2 = numeric2;
                    }
                    else
                    {
                        var numeric2 = new NumericUpDown
                        {
                            Dock = DockStyle.Fill,
                            Minimum = 0,
                            Maximum = 999999,
                            Margin = new Padding(3)
                        };
                        numeric2.ValueChanged += (s, e) => OnControlValueChanged(propertyName2, (int)numeric2.Value);
                        control2 = numeric2;
                    }
                    
                    _attributeControls[propertyName2] = control2;
                    
                    container.Controls.Add(label2, 2, i / 2);
                    container.Controls.Add(control2, 3, i / 2);
                }
            }
        }

        private void OnControlValueChanged(string propertyName, object value)
        {
            if (_isUpdating || _currentItem == null) return;

            try
            {
                // 使用反射设置属性值
                var property = typeof(GameItem).GetProperty(GetPropertyName(propertyName));
                if (property != null && property.CanWrite)
                {
                    // 类型转换
                    var convertedValue = Convert.ChangeType(value, property.PropertyType);
                    property.SetValue(_currentItem, convertedValue);
                    
                    _hasChanges = true;
                    ItemChanged?.Invoke(this, EventArgs.Empty);
                }
            }
            catch (Exception ex)
            {
                // 忽略转换错误，或者显示错误信息
                Console.WriteLine($"属性设置失败: {propertyName} = {value}, 错误: {ex.Message}");
            }
        }

        private string GetPropertyName(string controlName)
        {
            // 将控件名称映射到属性名称
            var mapping = new Dictionary<string, string>
            {
                ["id"] = "Id",
                ["name"] = "Name",
                ["desc"] = "Description",
                ["displayName"] = "DisplayName",
                ["itemType"] = "ItemType",
                ["weaponType"] = "WeaponType",
                ["mesh"] = "Mesh",
                ["material"] = "Material",
                ["iconName"] = "IconName",
                ["equipBone"] = "EquipBone",
                ["combatEquipBone"] = "CombatEquipBone",
                ["price"] = "Price",
                ["maxStackCount"] = "MaxStackCount",
                ["equipmentSlots"] = "EquipmentSlots",
                ["quality"] = "Quality",
                ["level"] = "Level",
                ["minDamage"] = "MinDamage",
                ["maxDamage"] = "MaxDamage",
                ["strength"] = "Strength",
                ["agility"] = "Agility",
                ["knowledge"] = "Knowledge",
                ["vitality"] = "Vitality",
                ["dexterity"] = "Dexterity",
                ["will"] = "Will",
                ["hitAccuracy"] = "HitAccuracy",
                ["critical"] = "Critical",
                ["block"] = "Block",
                ["parry"] = "Parry",
                ["dodge"] = "Dodge",
                ["magicalSkillBoost"] = "MagicalSkillBoost",
                ["magicalSkillBoostResist"] = "MagicalSkillBoostResist",
                ["magicalHitAccuracy"] = "MagicalHitAccuracy",
                ["attackType"] = "AttackType",
                ["attackDelay"] = "AttackDelay",
                ["hitCount"] = "HitCount",
                ["attackGap"] = "AttackGap",
                ["attackRange"] = "AttackRange"
            };
            
            return mapping.TryGetValue(controlName, out var propertyName) ? propertyName : controlName;
        }

        private void UpdateDisplay()
        {
            if (_currentItem == null)
            {
                ClearAllControls();
                return;
            }

            _isUpdating = true;
            try
            {
                UpdateBasicControls();
                UpdateAttributeControls();
                // TODO: 更新其他控件
            }
            finally
            {
                _isUpdating = false;
                _hasChanges = false;
            }
        }

        private void UpdateBasicControls()
        {
            if (_currentItem == null) return;

            SetControlValue("id", _currentItem.Id);
            SetControlValue("name", _currentItem.Name);
            SetControlValue("desc", _currentItem.Description);
            SetControlValue("displayName", _currentItem.DisplayName);
            SetControlValue("itemType", _currentItem.ItemType);
            SetControlValue("weaponType", _currentItem.WeaponType);
            SetControlValue("mesh", _currentItem.Mesh);
            SetControlValue("material", _currentItem.Material);
            SetControlValue("iconName", _currentItem.IconName);
            SetControlValue("equipBone", _currentItem.EquipBone);
            SetControlValue("combatEquipBone", _currentItem.CombatEquipBone);
            SetControlValue("price", _currentItem.Price);
            SetControlValue("maxStackCount", _currentItem.MaxStackCount);
            SetControlValue("equipmentSlots", _currentItem.EquipmentSlots);
            SetControlValue("quality", _currentItem.Quality);
            SetControlValue("level", _currentItem.Level);
        }

        private void UpdateAttributeControls()
        {
            if (_currentItem == null) return;

            SetControlValue("minDamage", _currentItem.MinDamage);
            SetControlValue("maxDamage", _currentItem.MaxDamage);
            SetControlValue("strength", _currentItem.Strength);
            SetControlValue("agility", _currentItem.Agility);
            SetControlValue("knowledge", _currentItem.Knowledge);
            SetControlValue("vitality", _currentItem.Vitality);
            SetControlValue("dexterity", _currentItem.Dexterity);
            SetControlValue("will", _currentItem.Will);
            SetControlValue("hitAccuracy", _currentItem.HitAccuracy);
            SetControlValue("critical", _currentItem.Critical);
            SetControlValue("block", _currentItem.Block);
            SetControlValue("parry", _currentItem.Parry);
            SetControlValue("dodge", _currentItem.Dodge);
            SetControlValue("magicalSkillBoost", _currentItem.MagicalSkillBoost);
            SetControlValue("magicalSkillBoostResist", _currentItem.MagicalSkillBoostResist);
            SetControlValue("magicalHitAccuracy", _currentItem.MagicalHitAccuracy);
            SetControlValue("attackType", _currentItem.AttackType);
            SetControlValue("attackDelay", _currentItem.AttackDelay);
            SetControlValue("hitCount", _currentItem.HitCount);
            SetControlValue("attackGap", _currentItem.AttackGap);
            SetControlValue("attackRange", _currentItem.AttackRange);
        }

        private void SetControlValue(string controlName, object value)
        {
            var allControls = _basicControls.Concat(_attributeControls).Concat(_classControls).Concat(_otherControls);
            
            if (allControls.FirstOrDefault(kvp => kvp.Key == controlName).Value is Control control)
            {
                switch (control)
                {
                    case TextBox textBox:
                        textBox.Text = value?.ToString() ?? string.Empty;
                        break;
                    case NumericUpDown numeric:
                        if (decimal.TryParse(value?.ToString(), out var numValue))
                            numeric.Value = Math.Max(numeric.Minimum, Math.Min(numeric.Maximum, numValue));
                        break;
                    case ComboBox combo:
                        combo.Text = value?.ToString() ?? string.Empty;
                        break;
                    case CheckBox checkBox:
                        if (value is bool boolValue)
                            checkBox.Checked = boolValue;
                        else if (value is int intValue)
                            checkBox.Checked = intValue != 0;
                        break;
                }
            }
        }

        private void ClearAllControls()
        {
            var allControls = _basicControls.Values
                .Concat(_attributeControls.Values)
                .Concat(_classControls.Values)
                .Concat(_otherControls.Values);

            foreach (var control in allControls)
            {
                switch (control)
                {
                    case TextBox textBox:
                        textBox.Clear();
                        break;
                    case NumericUpDown numeric:
                        numeric.Value = numeric.Minimum;
                        break;
                    case ComboBox combo:
                        combo.SelectedIndex = -1;
                        combo.Text = string.Empty;
                        break;
                    case CheckBox checkBox:
                        checkBox.Checked = false;
                        break;
                }
            }
        }

        public void SaveChanges()
        {
            if (_hasChanges)
            {
                ItemSaved?.Invoke(this, EventArgs.Empty);
                _hasChanges = false;
            }
        }

        public void DiscardChanges()
        {
            if (_hasChanges)
            {
                UpdateDisplay();
            }
        }
    }
}
