using GameItemEditor.Forms;
using System.Text;

namespace GameItemEditor
{
    /// <summary>
    /// 程序入口点
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点
        /// </summary>
        [STAThread]
        static void Main()
        {
            // 注册编码提供程序以支持中文编码
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
            
            // 启用应用程序视觉样式
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            // 设置高DPI支持
            Application.SetHighDpiMode(HighDpiMode.SystemAware);
            
            try
            {
                // 运行主窗体
                Application.Run(new MainForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"程序启动失败：{ex.Message}\n\n详细信息：{ex}",
                    "错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }
    }
}
