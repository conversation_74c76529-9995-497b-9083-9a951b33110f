using GameItemEditor.Models;
using System.Text.RegularExpressions;

namespace GameItemEditor.Services
{
    /// <summary>
    /// 搜索服务类
    /// 提供高级搜索和模糊匹配功能
    /// </summary>
    public class SearchService
    {
        private readonly GameDataService _gameDataService;

        public SearchService(GameDataService gameDataService)
        {
            _gameDataService = gameDataService;
        }

        /// <summary>
        /// 智能搜索
        /// 根据输入内容自动判断搜索类型
        /// </summary>
        public SearchResult SmartSearch(string searchText)
        {
            var result = new SearchResult();
            
            if (string.IsNullOrWhiteSpace(searchText))
            {
                result.Items = _gameDataService.GetAllItems();
                result.SearchType = "全部物品";
                result.Message = $"显示所有 {result.Items.Count} 个物品";
                return result;
            }

            searchText = searchText.Trim();

            // 1. 检查是否为纯数字（物品ID）
            if (int.TryParse(searchText, out var id))
            {
                result.Items = _gameDataService.SearchById(id);
                result.SearchType = "ID搜索";
                result.Message = $"搜索ID: {id}，找到 {result.Items.Count} 个结果";
                return result;
            }

            // 2. 检查是否包含中文字符
            if (ContainsChinese(searchText))
            {
                result.Items = _gameDataService.SearchByChineseName(searchText);
                result.SearchType = "中文名称搜索";
                result.Message = $"搜索中文: \"{searchText}\"，找到 {result.Items.Count} 个结果";
                return result;
            }

            // 3. 英文名称搜索
            result.Items = _gameDataService.SearchByName(searchText);
            result.SearchType = "英文名称搜索";
            result.Message = $"搜索英文: \"{searchText}\"，找到 {result.Items.Count} 个结果";
            
            return result;
        }

        /// <summary>
        /// 模糊搜索
        /// 支持拼音首字母、部分匹配等
        /// </summary>
        public SearchResult FuzzySearch(string searchText, double threshold = 0.6)
        {
            var result = new SearchResult();
            
            if (string.IsNullOrWhiteSpace(searchText))
            {
                result.Items = _gameDataService.GetAllItems();
                result.SearchType = "模糊搜索";
                result.Message = "请输入搜索关键词";
                return result;
            }

            var allItems = _gameDataService.GetAllItems();
            var fuzzyMatches = new List<(GameItem item, double score)>();

            foreach (var item in allItems)
            {
                // 计算相似度分数
                var nameScore = CalculateSimilarity(searchText, item.Name);
                var displayNameScore = CalculateSimilarity(searchText, item.DisplayName);
                var descScore = CalculateSimilarity(searchText, item.Description);
                
                var maxScore = Math.Max(Math.Max(nameScore, displayNameScore), descScore);
                
                if (maxScore >= threshold)
                {
                    fuzzyMatches.Add((item, maxScore));
                }
            }

            // 按相似度排序
            result.Items = fuzzyMatches
                .OrderByDescending(x => x.score)
                .Select(x => x.item)
                .ToList();
            
            result.SearchType = "模糊搜索";
            result.Message = $"模糊搜索: \"{searchText}\"，找到 {result.Items.Count} 个相似结果";
            
            return result;
        }

        /// <summary>
        /// 高级搜索
        /// 支持多条件组合搜索
        /// </summary>
        public SearchResult AdvancedSearch(AdvancedSearchCriteria criteria)
        {
            var result = new SearchResult();
            
            result.Items = _gameDataService.CombinedSearch(
                criteria.SearchText,
                criteria.ItemType,
                criteria.WeaponType,
                criteria.Quality,
                criteria.MinLevel,
                criteria.MaxLevel
            );
            
            result.SearchType = "高级搜索";
            result.Message = BuildAdvancedSearchMessage(criteria, result.Items.Count);
            
            return result;
        }

        /// <summary>
        /// 属性搜索
        /// 根据物品属性值搜索
        /// </summary>
        public SearchResult SearchByAttributes(AttributeSearchCriteria criteria)
        {
            var result = new SearchResult();
            var allItems = _gameDataService.GetAllItems();
            
            var query = allItems.AsQueryable();
            
            // 基础属性搜索
            if (criteria.MinStrength > 0)
                query = query.Where(item => item.Strength >= criteria.MinStrength);
            if (criteria.MaxStrength > 0)
                query = query.Where(item => item.Strength <= criteria.MaxStrength);
                
            if (criteria.MinAgility > 0)
                query = query.Where(item => item.Agility >= criteria.MinAgility);
            if (criteria.MaxAgility > 0)
                query = query.Where(item => item.Agility <= criteria.MaxAgility);
                
            if (criteria.MinKnowledge > 0)
                query = query.Where(item => item.Knowledge >= criteria.MinKnowledge);
            if (criteria.MaxKnowledge > 0)
                query = query.Where(item => item.Knowledge <= criteria.MaxKnowledge);
            
            // 伤害搜索
            if (criteria.MinDamage > 0)
                query = query.Where(item => item.MinDamage >= criteria.MinDamage);
            if (criteria.MaxDamage > 0)
                query = query.Where(item => item.MaxDamage <= criteria.MaxDamage);
            
            // 价格搜索
            if (criteria.MinPrice > 0)
                query = query.Where(item => item.Price >= criteria.MinPrice);
            if (criteria.MaxPrice > 0)
                query = query.Where(item => item.Price <= criteria.MaxPrice);
            
            result.Items = query.ToList();
            result.SearchType = "属性搜索";
            result.Message = $"属性搜索找到 {result.Items.Count} 个结果";
            
            return result;
        }

        /// <summary>
        /// 检查字符串是否包含中文字符
        /// </summary>
        private bool ContainsChinese(string text)
        {
            return Regex.IsMatch(text, @"[\u4e00-\u9fbb]");
        }

        /// <summary>
        /// 计算字符串相似度
        /// 使用编辑距离算法
        /// </summary>
        private double CalculateSimilarity(string source, string target)
        {
            if (string.IsNullOrEmpty(source) || string.IsNullOrEmpty(target))
                return 0.0;

            source = source.ToLower();
            target = target.ToLower();

            // 完全匹配
            if (source == target)
                return 1.0;

            // 包含匹配
            if (target.Contains(source) || source.Contains(target))
                return 0.8;

            // 编辑距离计算
            var distance = LevenshteinDistance(source, target);
            var maxLength = Math.Max(source.Length, target.Length);
            
            return 1.0 - (double)distance / maxLength;
        }

        /// <summary>
        /// 计算编辑距离
        /// </summary>
        private int LevenshteinDistance(string source, string target)
        {
            if (source.Length == 0) return target.Length;
            if (target.Length == 0) return source.Length;

            var matrix = new int[source.Length + 1, target.Length + 1];

            for (int i = 0; i <= source.Length; i++)
                matrix[i, 0] = i;

            for (int j = 0; j <= target.Length; j++)
                matrix[0, j] = j;

            for (int i = 1; i <= source.Length; i++)
            {
                for (int j = 1; j <= target.Length; j++)
                {
                    var cost = source[i - 1] == target[j - 1] ? 0 : 1;
                    matrix[i, j] = Math.Min(
                        Math.Min(matrix[i - 1, j] + 1, matrix[i, j - 1] + 1),
                        matrix[i - 1, j - 1] + cost);
                }
            }

            return matrix[source.Length, target.Length];
        }

        /// <summary>
        /// 构建高级搜索结果消息
        /// </summary>
        private string BuildAdvancedSearchMessage(AdvancedSearchCriteria criteria, int resultCount)
        {
            var conditions = new List<string>();
            
            if (!string.IsNullOrWhiteSpace(criteria.SearchText))
                conditions.Add($"关键词: {criteria.SearchText}");
            if (!string.IsNullOrWhiteSpace(criteria.ItemType))
                conditions.Add($"物品类型: {criteria.ItemType}");
            if (!string.IsNullOrWhiteSpace(criteria.WeaponType))
                conditions.Add($"武器类型: {criteria.WeaponType}");
            if (!string.IsNullOrWhiteSpace(criteria.Quality))
                conditions.Add($"品质: {criteria.Quality}");
            if (criteria.MinLevel > 0 || criteria.MaxLevel < 999)
                conditions.Add($"等级: {criteria.MinLevel}-{criteria.MaxLevel}");
            
            var conditionText = conditions.Count > 0 ? string.Join(", ", conditions) : "无条件";
            return $"高级搜索 ({conditionText})，找到 {resultCount} 个结果";
        }
    }

    /// <summary>
    /// 搜索结果类
    /// </summary>
    public class SearchResult
    {
        public List<GameItem> Items { get; set; } = new List<GameItem>();
        public string SearchType { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public TimeSpan SearchTime { get; set; }
    }

    /// <summary>
    /// 高级搜索条件
    /// </summary>
    public class AdvancedSearchCriteria
    {
        public string SearchText { get; set; } = string.Empty;
        public string ItemType { get; set; } = string.Empty;
        public string WeaponType { get; set; } = string.Empty;
        public string Quality { get; set; } = string.Empty;
        public int MinLevel { get; set; } = 0;
        public int MaxLevel { get; set; } = 999;
    }

    /// <summary>
    /// 属性搜索条件
    /// </summary>
    public class AttributeSearchCriteria
    {
        public int MinStrength { get; set; }
        public int MaxStrength { get; set; }
        public int MinAgility { get; set; }
        public int MaxAgility { get; set; }
        public int MinKnowledge { get; set; }
        public int MaxKnowledge { get; set; }
        public int MinDamage { get; set; }
        public int MaxDamage { get; set; }
        public int MinPrice { get; set; }
        public int MaxPrice { get; set; }
    }
}
